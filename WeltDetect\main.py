# 工业CT小球分割系统
import cv2
import numpy as np
import matplotlib
matplotlib.use('TkAgg')
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
import time
import os

from src.data_loader import DataLoader
from src.auto_focus import AutoFocusProcessor
from src.ai_inference import ai_init, warpper_img, inference
from src.sphere_segmentation import SphereSegmentation
from src.utils import load_config, ensure_directories, validate_model_file

def load_processed_data(data_path):
    """加载处理后的数据"""
    if not os.path.exists(data_path):
        print(f"错误: 数据路径不存在: {data_path}")
        return None

    ini_file = os.path.join(data_path, "ImageParam.ini")
    if os.path.exists(ini_file):
        print(f"加载bin格式数据: {data_path}")
        try:
            config = load_config()
            data_loader = DataLoader(config)
            volume_data = data_loader.load_volume_from_ini(data_path)
            print(f"数据加载成功，形状: {volume_data.shape}")
            return volume_data
        except Exception as e:
            print(f"数据加载失败: {e}")
            return None

    png_files = [f for f in os.listdir(data_path) if f.lower().endswith('.png')]
    if png_files:
        print(f"加载PNG格式数据: {data_path}")
        try:
            from src.auto_focus import load_volume_from_png_folder
            volume_data = load_volume_from_png_folder(data_path)
            print(f"数据加载成功，形状: {volume_data.shape}")
            return volume_data
        except Exception as e:
            print(f"数据加载失败: {e}")
            return None

    print(f"错误: 未找到支持的数据格式")
    return None

def check_detection_overlap(detections, debug_mode=True):
    """检查检测框重叠情况"""
    if not debug_mode:
        return 0
        
    overlap_count = 0
    print("检查检测框重叠...")
    for i, det1 in enumerate(detections):
        x1_1, y1_1, x2_1, y2_1 = map(int, det1[:4])
        for j, det2 in enumerate(detections[i+1:], i+1):
            x1_2, y1_2, x2_2, y2_2 = map(int, det2[:4])
            
            if not (x2_1 < x1_2 or x2_2 < x1_1 or y2_1 < y1_2 or y2_2 < y1_1):
                overlap_count += 1
                if overlap_count <= 5:
                    print(f"  重叠框 {i+1}-{j+1}")
    
    print(f"  发现 {overlap_count} 对重叠检测框")
    return overlap_count

def generate_seed_points(detections, target_layer, debug_mode=True):
    """为检测框生成种子点"""
    if debug_mode:
        print(f"生成 {len(detections)} 个种子点...")
    
    seed_points = []
    for i, det in enumerate(detections):
        x1, y1, x2, y2 = map(int, det[:4])
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2
        seed_points.append((target_layer, center_y, center_x))
    
    return seed_points

def create_sphere_segmenter(debug_mode=True, **params):
    """创建球体分割器"""
    default_params = {
        'gradient_threshold': 50,
        'min_sphere_size': 100,
        'intensity_tolerance_low': 0.85,
        'intensity_tolerance_high': 1.15,
        'max_radius': 8,
        'debug_mode': debug_mode
    }
    
    default_params.update(params)
    
    if debug_mode:
        print("球体分割器参数:")
        print(f"  强度范围: {default_params['intensity_tolerance_low']:.2f} - {default_params['intensity_tolerance_high']:.2f}")
        print(f"  最大半径: {default_params['max_radius']} 像素")
        print(f"  最小体积: {default_params['min_sphere_size']} 像素")
    
    return SphereSegmentation(**default_params)

def main(input_data_path=None, debug_mode=True, **segmentation_params):
    """主程序"""
    total_start_time = time.time()
    
    print("WeltDetect - 工业CT小球分割系统")
    print("=" * 40)
    
    if debug_mode:
        print("调试模式: 详细输出")
    else:
        print("生产模式: 仅最终结果")
    
    # 加载配置
    config = load_config()
    if debug_mode:
        print(f"使用设备: {config['device']['name']}")
    
    if input_data_path is None:
        input_data_path = "./data/processed/Slice_t"
    
    ensure_directories(config)
    model_available = validate_model_file(config['paths']['model_path'])
    
    # 初始化处理器
    auto_focus_processor = AutoFocusProcessor(config)
    sphere_segmenter = create_sphere_segmenter(debug_mode, **segmentation_params)
    
    # 1. 数据加载
    if debug_mode:
        print("\n开始数据加载...")
    data_load_start = time.time()
    volume_data = load_processed_data(input_data_path)
    data_load_time = time.time() - data_load_start
    
    if volume_data is None:
        print("错误: 数据加载失败")
        return
    
    if debug_mode:
        print(f"数据加载完成，耗时: {data_load_time:.2f}秒")
    
    # 2. 自动对焦
    if debug_mode:
        print("\n执行自动对焦...")
    focus_start = time.time()
    peak_indices = auto_focus_processor.find_focus_peaks(volume_data)
    focus_time = time.time() - focus_start
    
    if debug_mode:
        print(f"自动对焦完成，耗时: {focus_time:.2f}秒")
        print(f"找到 {len(peak_indices)} 个对焦峰值层")
    
    if len(peak_indices) == 0:
        middle_layer = volume_data.shape[0] // 2
        peak_indices = [middle_layer]
        if debug_mode:
            print(f"使用中间层: {middle_layer}")
    
    # 3. AI推理
    target_layer = 67
    if debug_mode:
        print(f"\n处理第{target_layer}层...")
    
    model = None
    if model_available:
        if debug_mode:
            print("初始化AI模型...")
        model = ai_init(config['paths']['model_path'], config['device']['name'])
    
    slice_data = volume_data[target_layer, :, :]
    
    if debug_mode:
        print(f"\n开始AI推理...")
    ai_start = time.time()
    detections = []
    
    if model_available and model is not None:
        try:
            img = warpper_img(slice_data)
            result = inference(model, img, config['device']['name'])
            
            if result and len(result) > 0 and len(result[0]) > 0:
                if hasattr(result[0], 'cpu'):
                    detections = result[0].cpu().numpy()
                else:
                    detections = result[0]
            
            ai_time = time.time() - ai_start
            
            if debug_mode:
                print(f"AI推理完成，耗时: {ai_time:.2f}秒")
                print(f"检测到 {len(detections)} 个目标")
            
        except Exception as e:
            ai_time = time.time() - ai_start
            if debug_mode:
                print(f"AI推理失败: {e}")
            detections = []
    else:
        ai_time = 0
        if debug_mode:
            print("跳过AI推理")
    
    if len(detections) == 0:
        print("没有检测到目标，使用模拟检测框进行测试")
        # 创建一些模拟的检测框用于测试3D分割功能
        detections = [
            [400, 400, 500, 500, 0.9],  # x1, y1, x2, y2, confidence
            [600, 300, 700, 400, 0.8],
            [200, 600, 300, 700, 0.85]
        ]
        detections = np.array(detections)
        print(f"使用 {len(detections)} 个模拟检测框进行测试")
    
    # 4. 检测框重叠分析
    if debug_mode:
        check_detection_overlap(detections, debug_mode)
    
    # 5. 种子点生成
    seed_points = generate_seed_points(detections, target_layer, debug_mode)
    
    # 6. 3D区域生长分割
    print("\n开始3D区域生长分割...")
    segmentation_start = time.time()
    sphere_masks, _ = sphere_segmenter.segment_spheres(volume_data, detections, target_layer)
    segmentation_time = time.time() - segmentation_start
    
    if len(sphere_masks) == 0:
        print(f"3D区域生长失败")
        return
    
    print(f"3D区域生长完成，耗时: {segmentation_time:.2f}秒")
    print(f"成功生成 {len(sphere_masks)} 个球体")
    
    # 7. 保存3D分割结果
    print("\n保存3D分割结果...")
    save_3d_start = time.time()
    output_path, _ = sphere_segmenter.save_3d_segmentation_result(volume_data, sphere_masks, "./data/output")
    save_3d_time = time.time() - save_3d_start
    print(f"3D数据保存完成，耗时: {save_3d_time:.2f}秒")
    
    # 8. 时间统计
    total_time = time.time() - total_start_time
    print(f"\n时间统计:")
    print(f"  数据加载: {data_load_time:.2f}秒")
    print(f"  自动对焦: {focus_time:.2f}秒")
    if model_available:
        print(f"  AI推理: {ai_time:.2f}秒")
    print(f"  3D分割: {segmentation_time:.2f}秒")
    print(f"  数据保存: {save_3d_time:.2f}秒")
    print(f"  总耗时: {total_time:.2f}秒")
    
    print(f"\n球体分割完成")
    print(f"输出文件: {output_path}")
    
    return {
        'sphere_count': len(sphere_masks),
        'total_time': total_time,
        'output_path': output_path
    }

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='WeltDetect - 工业CT小球分割系统')
    parser.add_argument('--input', '-i', type=str, default='./data/processed/Slice_t', help='输入数据路径')
    parser.add_argument('--debug', '-d', action='store_true', help='开启调试模式')
    parser.add_argument('--production', '-p', action='store_true', help='生产模式')
    parser.add_argument('--intensity-low', type=float, default=0.85, help='强度下限容忍度')
    parser.add_argument('--intensity-high', type=float, default=1.15, help='强度上限容忍度')
    parser.add_argument('--max-radius', type=int, default=8, help='最大生长半径')
    parser.add_argument('--min-size', type=int, default=100, help='最小球体体积')
    
    args = parser.parse_args()
    
    debug_mode = not args.production if args.production else True
    
    segmentation_params = {
        'intensity_tolerance_low': args.intensity_low,
        'intensity_tolerance_high': args.intensity_high,
        'max_radius': args.max_radius,
        'min_sphere_size': args.min_size
    }
    
    print(f"输入数据: {args.input}")
    print(f"运行模式: {'调试' if debug_mode else '生产'}")
    
    result = main(args.input, debug_mode, **segmentation_params)
    
    if result:
        print(f"最终结果: 成功分割 {result['sphere_count']} 个球体")
