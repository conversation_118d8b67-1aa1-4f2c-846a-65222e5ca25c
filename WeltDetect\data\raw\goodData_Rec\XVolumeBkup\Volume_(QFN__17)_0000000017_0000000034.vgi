{volume1}
[representation]
size = 480 440 66
datatype = unsigned integer
bitsperelement = 8
[file1]
SkipHeader = 0
FileFormat = raw
size = 480 440 66
Name = Volume_(QFN__17)_0000000017_0000000034.rec2
datatype = unsigned integer
BitsPerElement = 8
{volumeprimitive1}
[geometry]
resolution = 20 20 20
unit = um
[volume]
volume = volume1
[description]
text = Volume_(QFN__17)_0000000017_0000000034.rec2[1]
{camera1}
[rendering]
algorithm = maximum
{scene}
[rendering]
algorithm = maximum
