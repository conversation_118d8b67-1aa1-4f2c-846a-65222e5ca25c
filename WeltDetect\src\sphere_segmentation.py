"""
3D球体分割模块
基于区域生长的球体分割算法
"""
import numpy as np
import cv2
from scipy import ndimage
from scipy.ndimage import binary_fill_holes
from skimage.measure import label, regionprops
from skimage.morphology import binary_closing, binary_opening, ball
from skimage.segmentation import flood_fill
from concurrent.futures import ThreadPoolExecutor
import time

class SphereSegmentation:
    def __init__(self, gradient_threshold=100, min_sphere_size=30,
                 intensity_tolerance_low=0.85, intensity_tolerance_high=1.15,
                 max_radius=10, debug_mode=True):
        """
        初始化球体分割器

        Args:
            gradient_threshold: 区域生长的梯度阈值（目前未使用，保留接口）
            min_sphere_size: 最小球体体素数（3D总像素数，过滤小噪声）
            intensity_tolerance_low: 强度下限容忍度（种子CT值的倍数，如0.85表示种子值的85%）
            intensity_tolerance_high: 强度上限容忍度（种子CT值的倍数，如1.15表示种子值的115%）
            max_radius: 最大生长半径（从种子点向各方向的最大距离，单位：像素）
            debug_mode: 调试模式开关（控制详细输出和中间结果保存）

        区域生长条件说明:
        1. 强度条件: 像素CT值必须在 [种子值*low, 种子值*high] 范围内
        2. 距离条件: 像素必须在种子点的max_radius范围内（3D球形区域）
        3. 连通性条件: 像素必须与种子点6连通（上下左右前后相邻）
        4. 大小过滤: 最终生长区域必须≥min_sphere_size个像素

        注意: 目前未使用梯度条件，主要依靠强度阈值和连通性
        """
        self.gradient_threshold = gradient_threshold  # 保留但未使用
        self.min_sphere_size = min_sphere_size
        self.intensity_tolerance_low = intensity_tolerance_low
        self.intensity_tolerance_high = intensity_tolerance_high
        self.max_radius = max_radius
        self.debug_mode = debug_mode

        if debug_mode:
            print(f"球体分割器参数:")
            print(f"  强度容忍度: {intensity_tolerance_low:.2f} - {intensity_tolerance_high:.2f}")
            print(f"  最大半径: {max_radius} 像素")
            print(f"  最小球体大小: {min_sphere_size} 像素")
            print(f"  区域生长条件: 强度阈值 + 6连通 + 距离限制")
    
    def find_seed_points(self, volume_data, detections, focus_layer):
        """
        为每个检测到的圆形找到种子点 - 确保一一对应

        Args:
            volume_data: 3D体数据 (Z, H, W)
            detections: YOLO检测结果 [[x1,y1,x2,y2,conf,cls], ...]
            focus_layer: 对焦层索引

        Returns:
            seed_points: [(z, y, x), ...] 种子点列表，与detections一一对应
        """
        if len(detections) == 0:
            return []

        focus_slice = volume_data[focus_layer]
        seed_points = []

        print(f"为 {len(detections)} 个检测框生成种子点...")

        for i, det in enumerate(detections):
            x1, y1, x2, y2 = map(int, det[:4])

            # 确保检测框坐标有效
            x1 = max(0, min(x1, focus_slice.shape[1] - 1))
            y1 = max(0, min(y1, focus_slice.shape[0] - 1))
            x2 = max(x1 + 1, min(x2, focus_slice.shape[1]))
            y2 = max(y1 + 1, min(y2, focus_slice.shape[0]))

            # 提取检测框内的区域
            roi = focus_slice[y1:y2, x1:x2]

            if roi.size == 0:
                # 如果ROI为空，使用检测框中心
                seed_y = (y1 + y2) // 2
                seed_x = (x1 + x2) // 2
            else:
                # 找ROI内的最大值位置作为种子点
                max_pos = np.unravel_index(np.argmax(roi), roi.shape)
                seed_y = max_pos[0] + y1
                seed_x = max_pos[1] + x1

            # 确保种子点在图像范围内
            seed_y = max(0, min(seed_y, focus_slice.shape[0] - 1))
            seed_x = max(0, min(seed_x, focus_slice.shape[1] - 1))

            # 确保种子点在对应的检测框内
            if not (x1 <= seed_x < x2 and y1 <= seed_y < y2):
                # 如果种子点不在检测框内，强制使用检测框中心
                seed_y = (y1 + y2) // 2
                seed_x = (x1 + x2) // 2

            seed_point = (focus_layer, seed_y, seed_x)
            seed_points.append(seed_point)

            # 调试信息：每100个打印一次进度
            if (i + 1) % 100 == 0:
                print(f"  已处理 {i + 1}/{len(detections)} 个检测框")

        print(f"种子点生成完成: {len(seed_points)} 个")
        return seed_points
    
    def region_growing_3d_improved(self, volume_data, seed_point, detection_box, max_radius=15):
        """
        改进的3D区域生长算法 - 基于阈值分割 + 连通性检查

        Args:
            volume_data: 3D体数据
            seed_point: 种子点 (z, y, x)
            detection_box: 检测框 [x1, y1, x2, y2]
            max_radius: 最大生长半径

        Returns:
            mask: 3D分割mask
        """
        Z, H, W = volume_data.shape
        seed_z, seed_y, seed_x = seed_point
        x1, y1, x2, y2 = map(int, detection_box[:4])

        if not (0 <= seed_z < Z and 0 <= seed_y < H and 0 <= seed_x < W):
            return np.zeros_like(volume_data, dtype=bool)

        # 扩展检测框作为约束区域
        margin = 5
        x1_exp = max(0, x1 - margin)
        y1_exp = max(0, y1 - margin)
        x2_exp = min(W, x2 + margin)
        y2_exp = min(H, y2 + margin)

        # 限制3D搜索范围
        z_min = max(0, seed_z - max_radius)
        z_max = min(Z, seed_z + max_radius + 1)
        y_min = max(y1_exp, seed_y - max_radius)
        y_max = min(y2_exp, seed_y + max_radius + 1)
        x_min = max(x1_exp, seed_x - max_radius)
        x_max = min(x2_exp, seed_x + max_radius + 1)

        # 提取局部区域
        local_volume = volume_data[z_min:z_max, y_min:y_max, x_min:x_max]
        if local_volume.size == 0:
            return np.zeros_like(volume_data, dtype=bool)

        # 转换种子点到局部坐标
        local_seed = (seed_z - z_min, seed_y - y_min, seed_x - x_min)
        if not (0 <= local_seed[0] < local_volume.shape[0] and
                0 <= local_seed[1] < local_volume.shape[1] and
                0 <= local_seed[2] < local_volume.shape[2]):
            return np.zeros_like(volume_data, dtype=bool)

        seed_value = local_volume[local_seed]

        # 自适应阈值分割
        # 计算局部统计信息
        local_mean = np.mean(local_volume)
        local_std = np.std(local_volume)

        # 更严格的阈值范围
        lower_thresh = max(seed_value * 0.8, local_mean)
        upper_thresh = min(seed_value * 1.3, local_mean + local_std)

        # 创建初始阈值mask
        threshold_mask = (local_volume >= lower_thresh) & (local_volume <= upper_thresh)

        # 从种子点开始的连通性检查
        connected_mask = np.zeros_like(threshold_mask, dtype=bool)
        visited = np.zeros_like(threshold_mask, dtype=bool)

        # 使用BFS确保连通性
        queue = [local_seed]
        connected_mask[local_seed] = True
        visited[local_seed] = True

        # 26连通邻域
        neighbors = []
        for dz in [-1, 0, 1]:
            for dy in [-1, 0, 1]:
                for dx in [-1, 0, 1]:
                    if dz == 0 and dy == 0 and dx == 0:
                        continue
                    neighbors.append((dz, dy, dx))

        while queue:
            cz, cy, cx = queue.pop(0)

            for dz, dy, dx in neighbors:
                nz, ny, nx = cz + dz, cy + dy, cx + dx

                # 边界检查
                if not (0 <= nz < local_volume.shape[0] and
                       0 <= ny < local_volume.shape[1] and
                       0 <= nx < local_volume.shape[2]):
                    continue

                if visited[nz, ny, nx]:
                    continue

                visited[nz, ny, nx] = True

                # 如果在阈值范围内，加入连通区域
                if threshold_mask[nz, ny, nx]:
                    connected_mask[nz, ny, nx] = True
                    queue.append((nz, ny, nx))

        # 简单的形态学处理：填充小空洞
        final_mask = connected_mask.copy()

        # 使用ndimage进行闭运算（填充空洞）
        struct = np.ones((3, 3, 3), dtype=bool)
        final_mask = ndimage.binary_closing(final_mask, structure=struct, iterations=1)

        # 转换回全局mask
        global_mask = np.zeros_like(volume_data, dtype=bool)
        global_mask[z_min:z_max, y_min:y_max, x_min:x_max] = final_mask

        return global_mask

    def simple_region_growing(self, volume_data, seed_point, detection_box):
        """
        简单有效的3D区域生长算法

        Args:
            volume_data: 3D体数据
            seed_point: 种子点 (z, y, x)
            detection_box: 检测框 [x1, y1, x2, y2]
            max_radius: 最大生长半径

        Returns:
            mask: 3D分割mask
        """
        Z, H, W = volume_data.shape
        seed_z, seed_y, seed_x = seed_point
        x1, y1, x2, y2 = map(int, detection_box[:4])

        if not (0 <= seed_z < Z and 0 <= seed_y < H and 0 <= seed_x < W):
            return np.zeros_like(volume_data, dtype=bool)

        seed_value = volume_data[seed_z, seed_y, seed_x]

        # 限制搜索范围 - 使用实例变量
        z_min = max(0, seed_z - self.max_radius)
        z_max = min(Z, seed_z + self.max_radius + 1)
        y_min = max(0, seed_y - self.max_radius)
        y_max = min(H, seed_y + self.max_radius + 1)
        x_min = max(0, seed_x - self.max_radius)
        x_max = min(W, seed_x + self.max_radius + 1)

        # 创建局部mask
        local_shape = (z_max - z_min, y_max - y_min, x_max - x_min)
        local_mask = np.zeros(local_shape, dtype=bool)
        local_visited = np.zeros(local_shape, dtype=bool)

        # 转换种子点到局部坐标
        local_seed = (seed_z - z_min, seed_y - y_min, seed_x - x_min)

        # 使用简单的阈值分割
        local_volume = volume_data[z_min:z_max, y_min:y_max, x_min:x_max]

        # 计算阈值范围 - 使用更严格的参数
        lower_thresh = seed_value * self.intensity_tolerance_low
        upper_thresh = seed_value * self.intensity_tolerance_high

        # 调试信息（受debug_mode控制）
        if self.debug_mode and hasattr(self, '_debug_count') and self._debug_count < 3:
            print(f"    种子值: {seed_value}, 阈值范围: [{lower_thresh:.1f}, {upper_thresh:.1f}]")
            self._debug_count += 1

        # 创建阈值mask
        threshold_mask = (local_volume >= lower_thresh) & (local_volume <= upper_thresh)

        # 从种子点开始的连通区域
        queue = [local_seed]
        local_mask[local_seed] = True
        local_visited[local_seed] = True

        # 6连通邻域
        neighbors = [(-1,0,0), (1,0,0), (0,-1,0), (0,1,0), (0,0,-1), (0,0,1)]

        while queue:
            cz, cy, cx = queue.pop(0)

            for dz, dy, dx in neighbors:
                nz, ny, nx = cz + dz, cy + dy, cx + dx

                # 边界检查
                if not (0 <= nz < local_shape[0] and
                       0 <= ny < local_shape[1] and
                       0 <= nx < local_shape[2]):
                    continue

                if local_visited[nz, ny, nx]:
                    continue

                local_visited[nz, ny, nx] = True

                # 如果在阈值范围内，加入连通区域
                if threshold_mask[nz, ny, nx]:
                    local_mask[nz, ny, nx] = True
                    queue.append((nz, ny, nx))

        # 转换回全局mask
        global_mask = np.zeros_like(volume_data, dtype=bool)
        global_mask[z_min:z_max, y_min:y_max, x_min:x_max] = local_mask

        return global_mask
    
    def segment_spheres(self, volume_data, detections, focus_layer):
        """
        分割所有检测到的球体
        
        Args:
            volume_data: 3D体数据
            detections: YOLO检测结果
            focus_layer: 对焦层索引
            
        Returns:
            sphere_masks: 球体分割结果列表
            seed_points: 对应的种子点列表
        """
        print(f"开始球体分割，检测到 {len(detections)} 个目标")
        
        # 找种子点
        seed_points = self.find_seed_points(volume_data, detections, focus_layer)
        print(f"找到 {len(seed_points)} 个种子点")
        
        if len(seed_points) == 0:
            return [], []
        
        # 处理所有球体
        max_spheres = len(seed_points)  # 处理所有球体
        selected_seeds = seed_points

        print(f"处理所有 {max_spheres} 个球体")

        start_time = time.time()
        seed_generation_time = 0
        region_growing_time = 0

        # 串行处理以避免内存问题
        valid_masks = []
        valid_seeds = []
        failed_seeds = 0
        self._debug_count = 0  # 初始化调试计数器

        for i, seed_point in enumerate(selected_seeds):
            # 显示进度 - 适应更多球体
            if self.debug_mode and (i % 50 == 0 or i < 10):
                print(f"处理进度: {i+1}/{len(selected_seeds)}")

            # 获取对应的检测框
            detection_box = detections[i] if i < len(detections) else [0, 0, 100, 100]

            # 计时区域生长
            rg_start = time.time()
            mask = self.simple_region_growing(volume_data, seed_point, detection_box)
            region_growing_time += time.time() - rg_start

            mask_size = np.sum(mask)

            # 简化调试信息（受debug_mode控制）
            if self.debug_mode and i < 3:  # 只检查前3个
                seed_z = seed_point[0]
                layer_mask = mask[seed_z]
                layer_size = np.sum(layer_mask)
                print(f"  种子点 {i+1}: 总mask={mask_size}, 第{seed_z}层mask={layer_size}")

            # 统计失败的种子点
            if mask_size == 0:
                failed_seeds += 1

            if mask_size >= self.min_sphere_size:
                valid_masks.append(mask)
                valid_seeds.append(seed_point)
            else:
                if self.debug_mode and i < 10:  # 只打印前10个失败的
                    print(f"  种子点 {i+1}: 生长失败，mask太小 ({mask_size} < {self.min_sphere_size})")

        elapsed = time.time() - start_time
        success_rate = len(valid_masks) / len(selected_seeds) * 100

        if self.debug_mode:
            print(f"球体分割详细时间统计:")
            print(f"  区域生长总时间: {region_growing_time:.2f}s")
            print(f"  平均每球体: {region_growing_time/len(selected_seeds):.3f}s")
            print(f"  其他处理时间: {elapsed-region_growing_time:.2f}s")

        print(f"球体分割完成，耗时 {elapsed:.2f}s")
        print(f"成功分割: {len(valid_masks)}/{len(selected_seeds)} 个球体 ({success_rate:.1f}%)")
        if failed_seeds > 0:
            print(f"失败种子点: {failed_seeds} 个")

        return valid_masks, valid_seeds
    
    def visualize_results(self, volume_data, sphere_masks, focus_layer):
        """
        可视化分割结果
        
        Args:
            volume_data: 3D体数据
            sphere_masks: 球体分割结果
            focus_layer: 对焦层索引
            
        Returns:
            overlay_image: 叠加显示的图像
        """
        focus_slice = volume_data[focus_layer].copy()
        
        # 归一化到0-255
        focus_slice = ((focus_slice - focus_slice.min()) / 
                      (focus_slice.max() - focus_slice.min()) * 255).astype(np.uint8)
        
        # 转为RGB
        overlay = cv2.cvtColor(focus_slice, cv2.COLOR_GRAY2RGB)
        
        # 为每个球体分配不同颜色
        colors = [(255,0,0), (0,255,0), (0,0,255), (255,255,0), 
                 (255,0,255), (0,255,255), (128,0,128), (255,165,0)]
        
        for i, mask in enumerate(sphere_masks):
            color = colors[i % len(colors)]
            
            # 提取对焦层的mask
            layer_mask = mask[focus_layer]
            
            # 叠加颜色
            overlay[layer_mask] = overlay[layer_mask] * 0.7 + np.array(color) * 0.3
        
        return overlay

    def save_3d_segmentation_result(self, volume_data, sphere_masks, output_dir="./data/output"):
        """
        保存3D分割结果为ImageJ可读取的二进制文件

        Args:
            volume_data: 原始3D体数据
            sphere_masks: 球体分割mask列表
            output_dir: 输出目录
        """
        import os

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 获取数据维度
        Z, H, W = volume_data.shape

        # 创建合并的3D分割结果
        # 保留原始体素值，背景设为0
        print(f"正在生成3D分割结果...")
        print(f"原始数据维度: {Z} x {H} x {W}")
        print(f"原始数据类型: {volume_data.dtype}")
        print(f"球体数量: {len(sphere_masks)}")

        # 使用与原始数据相同的数据类型
        segmented_volume = np.zeros((Z, H, W), dtype=volume_data.dtype)

        # 合并所有球体mask，保留原始体素值
        combined_mask = np.zeros((Z, H, W), dtype=bool)
        for mask in sphere_masks:
            combined_mask |= mask

        # 只在球体区域保留原始体素值，背景为0
        segmented_volume[combined_mask] = volume_data[combined_mask]

        # 统计有效层数（包含分割结果的层）
        valid_slices = []
        for z in range(Z):
            if np.any(segmented_volume[z] > 0):
                valid_slices.append(z)

        print(f"有分割结果的层: {len(valid_slices)} 层 (第{min(valid_slices)}-{max(valid_slices)}层)")

        # 只保存有效层版本（节省时间和空间）
        data_type_str = str(volume_data.dtype)
        valid_volume = segmented_volume[valid_slices]
        valid_Z = len(valid_slices)

        filename_valid = f"segmented_spheres_original_values_valid_{W}x{H}x{valid_Z}.raw"
        output_path_valid = os.path.join(output_dir, filename_valid)

        valid_volume.tofile(output_path_valid)
        print(f"有效层分割结果已保存: {output_path_valid}")
        print(f"ImageJ打开参数: 宽度={W}, 高度={H}, 切片数={valid_Z}, 数据类型={data_type_str}")

        # 可选：如果需要完整版本，取消注释下面的代码
        # filename_full = f"segmented_spheres_original_values_{W}x{H}x{Z}.raw"
        # output_path_full = os.path.join(output_dir, filename_full)
        # segmented_volume.tofile(output_path_full)

        # 保存分割统计信息
        stats_file = os.path.join(output_dir, "segmentation_stats.txt")
        with open(stats_file, 'w', encoding='utf-8') as f:
            f.write("3D球体分割统计信息\n")
            f.write("=" * 30 + "\n")
            f.write(f"原始数据维度: {W} x {H} x {Z}\n")
            f.write(f"球体总数: {len(sphere_masks)}\n")
            f.write(f"有效层数: {len(valid_slices)}\n")
            f.write(f"有效层范围: {min(valid_slices)}-{max(valid_slices)}\n\n")

            f.write("ImageJ打开说明:\n")
            f.write("1. File -> Import -> Raw...\n")
            f.write("2. 选择对应的.raw文件\n")
            f.write("3. 设置参数:\n")
            f.write(f"   - Image type: {data_type_str}\n")
            f.write(f"   - Width: {W}\n")
            f.write(f"   - Height: {H}\n")
            f.write(f"   - Number of images: {Z} (完整版) 或 {valid_Z} (有效层版)\n")
            f.write("   - Little-endian byte order: 勾选\n\n")

            f.write("数据说明:\n")
            f.write("- 像素值 0: 背景区域\n")
            f.write("- 其他像素值: 球体区域的原始CT体素值\n")
            f.write("- 保留了球体的原始灰度信息，便于缺陷检测\n")

            # 统计每个球体的体积（优化版本，避免重复计算）
            f.write(f"\n球体体积统计:\n")
            if self.debug_mode:
                # 调试模式才计算详细体积
                for i, mask in enumerate(sphere_masks[:20]):  # 只显示前20个
                    volume = np.sum(mask)
                    f.write(f"球体 {i+1}: {volume} 像素\n")

                if len(sphere_masks) > 20:
                    f.write(f"... 还有 {len(sphere_masks)-20} 个球体\n")
            else:
                # 生产模式只显示总数
                f.write(f"总共 {len(sphere_masks)} 个球体（详细统计需开启调试模式）\n")

        print(f"分割统计信息已保存: {stats_file}")

        return output_path_valid, None  # 只返回有效层版本
