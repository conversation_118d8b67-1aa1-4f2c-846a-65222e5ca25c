from PIL import Image
import os
import numpy as np

# 超大文件裁剪
from PIL import ImageFile
ImageFile.LOAD_TRUNCATED_IMAGES = True

def extract_partial_pngs_with_missing_signature1(input_path, output_dir = None):
    with open(input_path, 'rb') as f:
        data = f.read()

    ihdr_pattern = b'IHDR'
    iend_pattern = b'IEND'
    png_signature = b'\x89PNG\r\n\x1a\n'

    offset = 0
    count = 0
    png_list = []
    while True:
        # 找到 IHDR 块
        ihdr_pos = data.find(ihdr_pattern, offset)
        if ihdr_pos == -1:
            break

        # PNG 的块格式是：长度(4字节) + 类型(4字节) + 数据 + CRC(4字节)
        # IHDR 前 4 字节是长度，我们往前退 4 字节找到 chunk 起始
        chunk_start = ihdr_pos - 4
        if chunk_start < 0:
            print(f"Invalid IHDR position at {ihdr_pos}")
            break

        # 从 IHDR 开始，向后找 IEND 结束块
        iend_pos = data.find(iend_pattern, ihdr_pos)
        if iend_pos == -1:
            print("IEND not found")
            break
        # IEND 块也是：4字节长度 + 'IEND' + 0字节数据 + 4字节CRC = 12 字节
        chunk_end = iend_pos + 8  # 4字节'IEND' + 4字节CRC

        png_data = png_signature + data[chunk_start:chunk_end]
        if output_dir is not None:
            output_file = os.path.join(output_dir, f"slice_{count:03}.png")
            with open(output_file, 'wb') as out:
                out.write(png_data)
        uint8_array = np.frombuffer(png_data, dtype=np.uint8)
        png_list.append( uint8_array )
        print(f"Extracted PNG slice {count} from offset {chunk_start} to {chunk_end}")
        offset = chunk_end
        count += 1

    print(f"✅ 提取完毕，共提取 {count} 张图像")
    return png_list

def extract_partial_pngs_with_missing_signature(input_path, output_dir):
    with open(input_path, 'rb') as f:
        data = f.read()
    os.makedirs(output_dir, exist_ok=True)
    ihdr_pattern = b'IHDR'
    iend_pattern = b'IEND'
    png_signature = b'\x89PNG\r\n\x1a\n'

    offset = 0
    count = 0
    while True:
        # 找到 IHDR 块
        ihdr_pos = data.find(ihdr_pattern, offset)
        if ihdr_pos == -1:
            break

        # PNG 的块格式是：长度(4字节) + 类型(4字节) + 数据 + CRC(4字节)
        # IHDR 前 4 字节是长度，我们往前退 4 字节找到 chunk 起始
        chunk_start = ihdr_pos - 4
        if chunk_start < 0:
            print(f"Invalid IHDR position at {ihdr_pos}")
            break

        # 从 IHDR 开始，向后找 IEND 结束块
        iend_pos = data.find(iend_pattern, ihdr_pos)
        if iend_pos == -1:
            print("IEND not found")
            break
        # IEND 块也是：4字节长度 + 'IEND' + 0字节数据 + 4字节CRC = 12 字节
        chunk_end = iend_pos + 8  # 4字节'IEND' + 4字节CRC

        png_data = png_signature + data[chunk_start:chunk_end]
        output_file = os.path.join(output_dir, f"slice_long_{count:03}.png")
        with open(output_file, 'wb') as out:
            out.write(png_data)

        print(f"Extracted PNG slice {count} from offset {chunk_start} to {chunk_end}")
        offset = chunk_end
        count += 1

    print(f"✅ 提取完毕，共提取 {count} 张图像")

def split_long_png_into_slices(png_path, output_dir, slice_height=536):
    os.makedirs(output_dir, exist_ok=True)
    img = Image.open(png_path)
    width, height = img.size

    num_slices = height // slice_height
    print(f"{png_path} , 图像尺寸：{width}x{height}，将分割为 {num_slices} 张切片")
    slice_list = []
    for i in range(num_slices):
        box = (0, i * slice_height, width, (i + 1) * slice_height)
        slice_img = img.crop(box)
        slice_img.save(os.path.join(output_dir, f"slice_{i:03}.png"))
        slice_list.append(img)

    print("✅ 分割完成")
    return slice_list

if __name__ == '__main__':
  root_path = "/data/lijunlin/data/CT/PCB/rec/"

vt_name_list = os.listdir(root_path)

for vt_name in vt_name_list:
    rec_file_list = os.listdir( root_path + vt_name)
    vt_rec_file_list = []
    for i in range(len(rec_file_list)):
        if rec_file_list[i][-4:] == "rec2":
            output_folder = root_path[:-1] + "_png/" + vt_name + "/" + rec_file_list[i][:-5] + "/"
            ext_png_list = extract_partial_pngs_with_missing_signature(root_path + vt_name + "/" + rec_file_list[i], output_folder)
            ext_png_list = os.listdir(output_folder)
            for ext_png in ext_png_list:
                slice_png_list = split_long_png_into_slices(output_folder + ext_png, output_folder)
