# WeltDetect - Industrial CT Ball Segmentation System Dependencies

# Basic scientific computing packages
numpy>=1.21.0,<2.0.0
opencv-python>=4.5.0,<5.0.0
Pillow>=9.0.0,<11.0.0
matplotlib>=3.5.0,<4.0.0

# Deep learning frameworks
torch>=1.12.0,<3.0.0
torchvision>=0.13.0,<1.0.0

# YOLOv5 related - fix compatibility issues
yolov5>=7.0.0,<8.0.0
ultralytics>=8.0.0,<9.0.0

# Configuration file processing
PyYAML>=6.0,<7.0.0

# Image processing enhancement
scikit-image>=0.19.0,<1.0.0

# Data processing
pandas>=1.3.0,<3.0.0

# Progress bar
tqdm>=4.64.0,<5.0.0

# Fix backports compatibility issue
backports.tarfile>=1.0.0; python_version<"3.12"

# Additional dependencies for model loading
gitpython>=3.1.30
requests>=2.25.0
setuptools>=65.0.0

# Optional: Jupyter support (if notebook development is needed)
# jupyter>=1.0.0
# ipykernel>=6.0.0

# Development and debugging tools (optional)
# tensorboard>=2.4.0
# seaborn>=0.11.0
