# WeltDetect - Industrial CT Ball Segmentation System Dependencies

# Basic scientific computing packages
numpy>=1.21.0
opencv-python>=4.5.0
Pillow>=9.0.0
matplotlib>=3.5.0

# Deep learning frameworks
torch>=1.12.0
torchvision>=0.13.0

# YOLOv5 related
yolov5>=7.0.0

# Configuration file processing
PyYAML>=6.0

# Image processing enhancement
scikit-image>=0.19.0

# Data processing
pandas>=1.3.0

# Progress bar
tqdm>=4.64.0

# Optional: Jupyter support (if notebook development is needed)
# jupyter>=1.0.0
# ipykernel>=6.0.0
