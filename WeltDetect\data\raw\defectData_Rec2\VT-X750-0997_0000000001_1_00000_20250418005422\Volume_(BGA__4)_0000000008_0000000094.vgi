{volume1}
[representation]
size = 536 536 81
datatype = unsigned integer
bitsperelement = 8
[file1]
SkipHeader = 0
FileFormat = raw
size = 536 536 81
Name = Volume_(BGA__4)_0000000008_0000000094.rec2
datatype = unsigned integer
BitsPerElement = 8
{volumeprimitive1}
[geometry]
resolution = 20 20 20
unit = um
[volume]
volume = volume1
[description]
text = Volume_(BGA__4)_0000000008_0000000094.rec2[1]
{camera1}
[rendering]
algorithm = maximum
{scene}
[rendering]
algorithm = maximum
