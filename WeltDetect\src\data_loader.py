"""
数据加载模块 - 处理rec和rec2文件的读取和转换
"""
import os
import numpy as np
import configparser
from PIL import Image, ImageFile
import cv2
from typing import Tuple, List, Optional

# 处理超大文件
ImageFile.LOAD_TRUNCATED_IMAGES = True


class DataLoader:
    """数据加载器类"""
    
    def __init__(self, config: dict):
        self.config = config
        self.data_root = config['paths']['raw_data']
        self.processed_root = config['paths']['processed_data']
        self.default_dtype = getattr(np, config['data_processing']['default_dtype'])
        
    def load_volume_from_ini(self, folder_path: str) -> np.ndarray:
        """从ini配置文件加载volume数据"""
        # 读取ini文件
        ini_path = os.path.join(folder_path, 'ImageParam.ini')
        if not os.path.exists(ini_path):
            raise FileNotFoundError(f"ImageParam.ini not found in {folder_path}")
            
        config = configparser.ConfigParser(interpolation=None)
        config.read(ini_path, encoding='utf-8')
        
        # 获取图像参数
        width = int(config['RawImageInfo']['Width'])
        height = int(config['RawImageInfo']['Height'])
        begin_index = int(config['FileModule']['BeginIndex'])
        end_index = int(config['FileModule']['EndIndex'])
        template = config['FileModule']['NameTemplate']
        
        # 像素数据类型
        bits_allocated = int(config['RawImageInfo']['BitsAllocated'])
        pixel_repr = int(config['RawImageInfo']['PixelRepresentation'])
        
        # 推断numpy dtype
        if bits_allocated == 16:
            dtype = np.int16 if pixel_repr == 1 else np.uint16
        elif bits_allocated == 8:
            dtype = np.uint8
        else:
            dtype = self.default_dtype
            
        # 初始化体数据数组
        depth = end_index - begin_index + 1
        volume = np.zeros((depth, height, width), dtype=dtype)
        
        # 读取每个切片
        for i in range(begin_index, end_index + 1):
            filename = template % i
            file_path = os.path.join(folder_path, filename)
            
            if not os.path.exists(file_path):
                print(f"Warning: {file_path} not found, skipping...")
                continue
                
            with open(file_path, 'rb') as f:
                slice_data = np.frombuffer(f.read(), dtype=dtype)
                if slice_data.size != width * height:
                    print(f"Warning: Slice {filename} size mismatch")
                    continue
                volume[i - begin_index] = slice_data.reshape((height, width))
        
        return volume
    
    def load_volume_from_vgi_rec(self, vgi_path: str) -> np.ndarray:
        """从vgi文件加载对应的rec文件"""
        # 解析vgi文件获取参数
        vgi_params = self._parse_vgi_file(vgi_path)
        
        # 构造rec文件路径
        rec_path = vgi_path.replace('.vgi', '.rec')
        if not os.path.exists(rec_path):
            raise FileNotFoundError(f"REC file not found: {rec_path}")
        
        # 读取rec文件
        return self._load_rec_file(rec_path, vgi_params)
    
    def _parse_vgi_file(self, vgi_path: str) -> dict:
        """解析vgi文件获取volume参数"""
        params = {}
        with open(vgi_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if '=' in line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    params[key.strip()] = value.strip()
        
        # 提取关键参数
        result = {
            'width': int(params.get('Size X', 0)),
            'height': int(params.get('Size Y', 0)),
            'depth': int(params.get('Size Z', 0)),
            'data_type': params.get('Data type', 'unsigned integer'),
            'bits': int(params.get('Bits per voxel', 16))
        }
        
        return result
    
    def _load_rec_file(self, rec_path: str, params: dict) -> np.ndarray:
        """加载rec文件"""
        width, height, depth = params['width'], params['height'], params['depth']
        bits = params['bits']
        
        # 确定数据类型
        if bits == 8:
            dtype = np.uint8
        elif bits == 16:
            dtype = np.uint16
        else:
            dtype = self.default_dtype
        
        # 读取数据
        with open(rec_path, 'rb') as f:
            data = np.frombuffer(f.read(), dtype=dtype)
            
        # 重塑为volume
        expected_size = width * height * depth
        if data.size != expected_size:
            print(f"Warning: Data size mismatch. Expected {expected_size}, got {data.size}")
            # 截断或填充数据
            if data.size > expected_size:
                data = data[:expected_size]
            else:
                padded_data = np.zeros(expected_size, dtype=dtype)
                padded_data[:data.size] = data
                data = padded_data
        
        volume = data.reshape((depth, height, width))
        return volume


class Rec2Processor:
    """rec2文件处理器"""
    
    def __init__(self, config: dict):
        self.config = config
        self.slice_height = config['data_processing']['slice_height']
        self.output_format = config['data_processing']['output_format']
        
    def extract_pngs_from_rec2(self, rec2_path: str, output_dir: str) -> List[str]:
        """从rec2文件提取PNG图像"""
        os.makedirs(output_dir, exist_ok=True)
        
        with open(rec2_path, 'rb') as f:
            data = f.read()
        
        ihdr_pattern = b'IHDR'
        iend_pattern = b'IEND'
        png_signature = b'\x89PNG\r\n\x1a\n'
        
        offset = 0
        count = 0
        extracted_files = []
        
        while True:
            # 找到IHDR块
            ihdr_pos = data.find(ihdr_pattern, offset)
            if ihdr_pos == -1:
                break
            
            # PNG块格式：长度(4字节) + 类型(4字节) + 数据 + CRC(4字节)
            chunk_start = ihdr_pos - 4
            if chunk_start < 0:
                break
            
            # 找到IEND结束块
            iend_pos = data.find(iend_pattern, ihdr_pos)
            if iend_pos == -1:
                break
            
            chunk_end = iend_pos + 8  # 4字节'IEND' + 4字节CRC
            
            # 构造完整PNG数据
            png_data = png_signature + data[chunk_start:chunk_end]
            
            # 保存PNG文件
            output_file = os.path.join(output_dir, f"slice_long_{count:03}.png")
            with open(output_file, 'wb') as out:
                out.write(png_data)
            
            extracted_files.append(output_file)
            print(f"Extracted PNG slice {count}")
            
            offset = chunk_end
            count += 1
        
        print(f"✅ 提取完毕，共提取 {count} 张图像")
        return extracted_files
    
    def split_long_png_to_slices(self, png_path: str, output_dir: str) -> List[str]:
        """将长PNG图像分割为切片"""
        os.makedirs(output_dir, exist_ok=True)
        
        img = Image.open(png_path)
        width, height = img.size
        
        num_slices = height // self.slice_height
        print(f"图像尺寸：{width}x{height}，将分割为 {num_slices} 张切片")
        
        slice_files = []
        for i in range(num_slices):
            box = (0, i * self.slice_height, width, (i + 1) * self.slice_height)
            slice_img = img.crop(box)
            
            slice_file = os.path.join(output_dir, f"slice_{i:03}.png")
            slice_img.save(slice_file)
            slice_files.append(slice_file)
        
        print("✅ 分割完成")
        return slice_files
    
    def process_rec2_to_volume(self, rec2_path: str, output_dir: str) -> np.ndarray:
        """处理rec2文件并转换为volume数据"""
        # 提取PNG文件
        temp_dir = os.path.join(output_dir, "temp")
        long_pngs = self.extract_pngs_from_rec2(rec2_path, temp_dir)
        
        # 分割PNG并加载为volume
        all_slices = []
        for long_png in long_pngs:
            slice_files = self.split_long_png_to_slices(long_png, output_dir)
            
            # 加载切片数据
            for slice_file in slice_files:
                slice_img = cv2.imread(slice_file, cv2.IMREAD_UNCHANGED)
                if slice_img is not None and (slice_img > 0).sum() > slice_img.size * 0.5:
                    all_slices.append(slice_img)
        
        # 清理临时文件
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
        
        if not all_slices:
            raise ValueError("No valid slices found in rec2 file")
        
        volume = np.array(all_slices)
        print(f"✅ 处理完成，volume shape: {volume.shape}")
        
        return volume
