"""
AI推理模块 - 重构版
"""
import cv2
import numpy as np
import torch
import os
import sys
from typing import List, Tuple, Optional, Dict, Any
import pathlib
temp = pathlib.PosixPath
pathlib.PosixPath = pathlib.WindowsPath
# 动态导入YOLOv5模块
try:
    from yolov5.utils.general import scale_boxes as scale_coords
    from yolov5.utils.general import non_max_suppression
    from yolov5.models.experimental import *
except ImportError:
    print("Warning: YOLOv5 not found. Please install: pip install yolov5")
    scale_coords = None
    non_max_suppression = None


def attempt_load(weights, device=None, inplace=True, fuse=True):
    # Loads an ensemble of models weights=[a,b,c] or a single model weights=[a] or weights=a
    from models.yolo import Detect, Model

    model = Ensemble()
    for w in weights if isinstance(weights, list) else [weights]:
        ckpt = torch.load(attempt_download(w), map_location='cpu', weights_only=False)  # load
        ckpt = (ckpt.get('ema') or ckpt['model']).to(device).float()  # FP32 model

        # Model compatibility updates
        if not hasattr(ckpt, 'stride'):
            ckpt.stride = torch.tensor([32.])
        if hasattr(ckpt, 'names') and isinstance(ckpt.names, (list, tuple)):
            ckpt.names = dict(enumerate(ckpt.names))  # convert to dict

        model.append(ckpt.fuse().eval() if fuse and hasattr(ckpt, 'fuse') else ckpt.eval())  # model in eval mode

    # Module compatibility updates
    for m in model.modules():
        t = type(m)
        if t in (nn.Hardswish, nn.LeakyReLU, nn.ReLU, nn.ReLU6, nn.SiLU, Detect, Model):
            m.inplace = inplace  # torch 1.7.0 compatibility
            if t is Detect and not isinstance(m.anchor_grid, list):
                delattr(m, 'anchor_grid')
                setattr(m, 'anchor_grid', [torch.zeros(1)] * m.nl)
        elif t is nn.Upsample and not hasattr(m, 'recompute_scale_factor'):
            m.recompute_scale_factor = None  # torch 1.11.0 compatibility

    # Return model
    if len(model) == 1:
        return model[-1]

    # Return detection ensemble
    print(f'Ensemble created with {weights}\n')
    for k in 'names', 'nc', 'yaml':
        setattr(model, k, getattr(model[0], k))
    model.stride = model[torch.argmax(torch.tensor([m.stride.max() for m in model])).int()].stride  # max stride
    assert all(model[0].nc == m.nc for m in model), f'Models have different class counts: {[m.nc for m in model]}'
    return model

def ai_init(weights="./models/best.pt", device="auto"):
    """初始化AI模型 - 跨平台自动适配"""
    import os
    if not os.path.exists(weights):
        print(f"错误: 模型文件未找到: {weights}")
        return None

    # 自动检测最优设备
    if device == "auto":
        if torch.cuda.is_available():
            device = "cuda:0"
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            device = "mps"
        else:
            device = "cpu"

    # 验证设备可用性
    if device.startswith("cuda") and not torch.cuda.is_available():
        print("CUDA不可用，切换到CPU")
        device = "cpu"
    elif device == "mps" and not (hasattr(torch.backends, 'mps') and torch.backends.mps.is_available()):
        print("MPS不可用，切换到CPU")
        device = "cpu"

    print(f"使用设备: {device}")

    try:
        # 方法1: 尝试使用ultralytics库（最新推荐方式）
        try:
            from ultralytics import YOLO
            model = YOLO(weights)
            if hasattr(model, 'to'):
                model.to(device)
            print(f"使用ultralytics库加载模型成功: {weights}")
            return model
        except Exception as e1:
            print(f"ultralytics库加载失败: {e1}")

            # 方法2: 尝试使用YOLOv5库
            try:
                import yolov5
                model = yolov5.load(weights, device=device)
                print(f"使用yolov5库加载模型成功: {weights}")
                return model
            except Exception as e2:
                print(f"yolov5库加载失败: {e2}")

                # 方法3: 尝试使用torch.hub
                try:
                    model = torch.hub.load('ultralytics/yolov5', 'custom', weights,
                                         device=device, trust_repo=True, force_reload=True)
                    print(f"使用torch.hub加载模型成功: {weights}")
                    return model
                except Exception as e3:
                    print(f"torch.hub加载失败: {e3}")

                    # 方法4: 最后尝试直接torch.load（处理模块依赖问题）
                    try:
                        import pickle
                        import warnings
                        import sys
                        import tempfile
                        import os

                        warnings.filterwarnings("ignore", category=FutureWarning)

                        # 创建临时的models模块来解决依赖问题
                        temp_dir = tempfile.mkdtemp()
                        models_dir = os.path.join(temp_dir, 'models')
                        os.makedirs(models_dir, exist_ok=True)

                        # 创建空的__init__.py
                        with open(os.path.join(models_dir, '__init__.py'), 'w') as f:
                            f.write('')

                        # 创建简单的yolo.py模块
                        with open(os.path.join(models_dir, 'yolo.py'), 'w') as f:
                            f.write('''
import torch
import torch.nn as nn

class Detect(nn.Module):
    def __init__(self):
        super().__init__()

class Model(nn.Module):
    def __init__(self):
        super().__init__()
''')

                        # 添加到sys.path
                        if temp_dir not in sys.path:
                            sys.path.insert(0, temp_dir)

                        # 现在尝试加载模型
                        checkpoint = torch.load(weights, map_location=device, weights_only=False, pickle_module=pickle)

                        # 清理临时目录
                        import shutil
                        shutil.rmtree(temp_dir, ignore_errors=True)

                        # 尝试不同的模型提取方式
                        if isinstance(checkpoint, dict):
                            model = checkpoint.get('model') or checkpoint.get('ema') or checkpoint.get('best_model')
                            if model is None and 'model' in checkpoint:
                                model = checkpoint['model']
                        else:
                            model = checkpoint

                        if model is not None and hasattr(model, 'to'):
                            model = model.to(device).float().eval()
                            print(f"使用torch.load加载模型成功: {weights}")
                            return model
                        else:
                            raise ValueError("无法从检查点中提取有效模型")

                    except Exception as e4:
                        print(f"torch.load加载失败: {e4}")
                        print("所有模型加载方法都失败了")
                        print("提示: 模型文件可能需要在原始训练环境中使用，或者需要重新训练")
                        raise RuntimeError("AI模型加载失败，程序退出")

    except Exception as e:
        print(f"模型加载失败: {e}")
        print("提示: 这可能是PyTorch 2.6的安全特性导致的")
        print("模型文件本身应该没问题，是加载方式的兼容性问题")
        return None


def warpper_img( img):
    if len(img.shape) == 2 or img.shape[-1] == 1:
        img = np.dstack( ( img, img, img)) 
    img_wappered = np.transpose(img, (2, 0, 1))
    img_wappered = ( img_wappered - img_wappered.min())/( img_wappered.max() - img_wappered.min() ) 
    return img_wappered

def inference(model, img0, device):
    """AI推理函数 - 修复参数类型问题"""
    try:
        img = np.expand_dims(img0, axis=0)    #扩展维度至[1,3,1024,1024]
        img = torch.from_numpy(img.copy())   #numpy转tensor
        img = img.to(torch.float32)          #float64转换float32
        img = img.to(device)

        # 修复参数类型：使用布尔值而不是字符串
        pred = model(img, augment=False, visualize=False)[0]
        pred = pred.clone().detach()

        pred = non_max_suppression(pred, 0.35, 0.45, None, False, max_det=1000)  #非极大值抑制
        # pred的长度为1 pred[0].shape 为 (256,6)  ( point number, x0,y0,x1,y1,conf, cls)
        result_l = []
        for i, det in enumerate(pred):
            if len(det):
                det[:, :4] = scale_coords(img.shape[2:], det[:, :4], img0.shape[1:]).round()
                result_l.append(det.cpu().numpy())
        return result_l
    except Exception as e:
        print(f"AI推理过程中发生错误: {e}")
        raise RuntimeError(f"AI推理失败: {e}")

def get_mask(pred, origin_shape):
    mask = np.zeros(origin_shape)
    for i, det in enumerate(pred):
        if len(det):
            for *xyxy, conf, cls in reversed(det):
                mask[ int(xyxy[1]):int(xyxy[3]), int(xyxy[0]):int(xyxy[2])] = 1
    return mask
    

def show_result(pred, img, img0):
    for i, det in enumerate(pred):
        if len(det):
            det[:, :4] = scale_coords(img.shape[1:], det[:, :4], img0.shape).round()
            for *xyxy, conf, cls in reversed(det):
                # print('{},{},{}'.format(xyxy, conf, cls)) #输出结果：xyxy检测框左上角和右下角坐标，conf置信度，cls分类结果
                img0 = cv2.rectangle(img0, (int(xyxy[0]), int(xyxy[1])), (int(xyxy[2]), int(xyxy[3])), (0, 255, 0), 2)
    # cv2.imwrite('out.jpg', img0)  #简单画个框
    return img0


if __name__ == "__main__":
    # 使用当前环境的配置
    DEVICE = "mps"  # Mac环境使用MPS
    WEIGHT_PATH = "./models/best.pt"  # 相对路径

    # 测试用的示例图像路径（如果存在的话）
    test_image_path = "./data/processed/test_image.png"

    if os.path.exists(test_image_path):
        img0 = cv2.imread(test_image_path, 0)
        img = warpper_img(img0)
        model = ai_init(WEIGHT_PATH, DEVICE)

        if model is not None:
            result = inference(model, img, DEVICE)
            mask = get_mask(result, img0.shape)
            rect_show = show_result(result, img, img0.copy())
            print("✅ AI推理测试完成")
        else:
            print("❌ 模型加载失败")
    else:
        print(f"⚠️  测试图像不存在: {test_image_path}")
        print("请先运行数据预处理脚本生成测试数据")
