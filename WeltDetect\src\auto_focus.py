"""
自动对焦算法模块 - 重构版
"""
import numpy as np
import cv2
from typing import List, Tuple, Optional


class AutoFocusProcessor:
    """自动对焦处理器"""
    
    def __init__(self, config: dict):
        self.config = config
        self.focus_threshold = config['auto_focus']['focus_threshold']
        self.grid_size = config['auto_focus']['grid_size']
        self.peak_range_ratio = config['auto_focus']['peak_range_ratio']
    
    def calculate_focus_score(self, image: np.ndarray) -> float:
        """计算单张图像的对焦得分"""
        # 转换为8bit以提高稳定性
        if image.dtype != np.uint8:
            norm_image = ((image - image.min()) / (image.max() - image.min()) * 255).astype(np.uint8)
        else:
            norm_image = image
        
        # 使用Laplacian方差作为对焦度量
        laplacian = cv2.Laplacian(norm_image, cv2.CV_64F)
        focus_score = laplacian.var()
        
        return focus_score
    
    def find_focus_peaks(self, volume: np.ndarray) -> List[int]:
        """找到volume中的对焦峰值层"""
        focus_scores = []
        
        # 计算每层的对焦得分
        for i in range(volume.shape[0]):
            slice_img = volume[i]
            score = self.calculate_focus_score(slice_img)
            focus_scores.append(score)
        
        # 二值化处理：高于阈值的设为原值，否则设为0
        binary_scores = []
        for score in focus_scores:
            if score > self.focus_threshold:
                binary_scores.append(score)
            else:
                binary_scores.append(0)
        
        # 在非零段中找峰值
        peak_indices = self._find_peaks_in_segments(binary_scores)
        
        return peak_indices
    
    def _find_peaks_in_segments(self, scores: List[float]) -> List[int]:
        """在非零段中找到峰值索引"""
        peaks = []
        n = len(scores)
        i = 0
        
        while i < n:
            # 跳过零值，寻找非零段的起始
            while i < n and scores[i] == 0:
                i += 1
            if i >= n:
                break
            
            # 记录非零段的起始和结束
            start = i
            while i < n and scores[i] != 0:
                i += 1
            end = i - 1
            
            # 在当前非零段中找峰值
            if start <= end:
                segment = scores[start:end+1]
                max_index = np.argmax(segment) + start
                peaks.append(max_index)
        
        return peaks
    
    def generate_grid_focus_image(self, volume: np.ndarray, grid_size: Optional[int] = None) -> Tuple[np.ndarray, np.ndarray]:
        """生成网格化最优对焦图像"""
        if grid_size is None:
            grid_size = self.grid_size
            
        num_layers, height, width = volume.shape
        
        # 计算tile大小
        tile_height = height // grid_size
        tile_width = width // grid_size
        
        if height % grid_size != 0 or width % grid_size != 0:
            print(f"Warning: Grid size {grid_size} doesn't divide evenly. Using floor division.")
        
        # 初始化输出
        output_image = np.zeros((height, width), dtype=volume.dtype)
        best_indices = np.zeros((grid_size, grid_size), dtype=np.int32)
        
        # 遍历每个网格位置
        for i in range(grid_size):
            for j in range(grid_size):
                # 当前网格的坐标范围
                h_start = i * tile_height
                h_end = min((i + 1) * tile_height, height)
                w_start = j * tile_width
                w_end = min((j + 1) * tile_width, width)
                
                # 寻找最优层
                best_score = -np.inf
                best_layer_idx = 0
                
                for layer_idx in range(num_layers):
                    tile = volume[layer_idx, h_start:h_end, w_start:w_end]
                    score = self.calculate_focus_score(tile)
                    
                    if score > best_score:
                        best_score = score
                        best_layer_idx = layer_idx
                
                # 将最优层的tile放入输出
                output_image[h_start:h_end, w_start:w_end] = volume[best_layer_idx, h_start:h_end, w_start:w_end]
                best_indices[i, j] = best_layer_idx
        
        return output_image, best_indices


# 兼容旧代码的函数
def auto_focus(volume_data: np.ndarray, focus_threshold: float = 100) -> List[int]:
    """兼容旧代码的自动对焦函数"""
    config = {
        'auto_focus': {
            'focus_threshold': focus_threshold,
            'grid_size': 4,
            'peak_range_ratio': 0.1
        }
    }
    
    processor = AutoFocusProcessor(config)
    return processor.find_focus_peaks(volume_data)


if __name__ == "__main__":
    print("AutoFocus module loaded successfully!")
