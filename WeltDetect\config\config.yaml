# WeltDetect 配置文件

# 路径配置
paths:
  # 数据路径
  data_root: "./data"
  raw_data: "./data/raw"
  processed_data: "./data/processed"
  output_data: "./data/output"
  
  # 模型路径
  model_path: "./models/best.pt"
  
  # 临时文件路径
  temp_dir: "./temp"

# 设备配置
device:
  # 使用的设备 (auto, cpu, cuda:0, mps)
  name: "auto"  # auto表示自动检测最优设备
  
# AI推理配置
ai_inference:
  # YOLO参数
  confidence_threshold: 0.35
  iou_threshold: 0.45
  max_detections: 1000
  
  # 图像预处理
  input_size: [640, 640]
  normalize: true

# 自动对焦配置
auto_focus:
  # 对焦评分阈值
  focus_threshold: 100
  
  # 网格化对焦参数
  grid_size: 4
  peak_range_ratio: 0.1  # 峰值范围占总层数的比例

# 数据处理配置
data_processing:
  # rec2解压参数
  slice_height: 536
  output_format: "png"
  
  # 数据类型
  default_dtype: "uint16"
  
# 3D处理配置 (待实现)
region_growing:
  gradient_threshold: 50
  max_radius: 100
  preserve_holes: true

# 硬化补偿配置 (待实现)
beam_hardening:
  enable: true
  correction_method: "polynomial"

# 输出配置
output:
  save_intermediate: true
  visualization: true
  export_format: ["png", "npy"]
