# WeltDetect - 工业CT小球分割系统

基于深度学习和3D区域生长的工业CT小球自动分割系统。

## 项目目的

本系统用于工业CT扫描数据中小球（如钢球、陶瓷球等）的自动检测和精确分割，主要应用场景：

- **质量检测**: 检测小球的完整性、缺陷、尺寸等
- **计数统计**: 自动统计小球数量和分布
- **3D重建**: 提取小球的精确3D形状用于进一步分析
- **批量处理**: 处理大量CT数据，提高检测效率

## 算法原理

### 核心思路
1. **2D目标检测**: 使用YOLO在单层CT图像上快速定位小球位置
2. **3D区域生长**: 以检测到的2D位置为种子点，在3D空间中精确分割球体
3. **多层融合**: 结合多个CT层的信息，获得完整的3D球体形状

### 关键算法步骤

#### 1. 数据预处理
- **自动对焦**: 基于梯度方差找到最清晰的CT层
- **数据标准化**: 统一不同设备的CT值范围
- **格式转换**: 支持多种CT数据格式（bin、PNG序列等）

#### 2. 2D目标检测
- **YOLO检测**: 在对焦层上检测小球的2D边界框
- **置信度过滤**: 去除低置信度的误检
- **重叠处理**: 处理重叠检测框，避免重复计数

#### 3. 种子点生成
- **中心计算**: 以检测框中心作为3D种子点
- **层间对应**: 建立2D检测与3D位置的对应关系
- **有效性验证**: 确保种子点位于有效的CT值范围内

#### 4. 3D区域生长分割
- **强度阈值**: 基于种子点CT值设定生长范围 [种子值×0.85, 种子值×1.15]
- **空间约束**: 限制生长半径（默认8像素），避免扩散到远处
- **连通性检查**: 确保生长区域与种子点6连通（上下左右前后）
- **形态学处理**: 填充空洞，平滑边界

#### 5. 后处理与输出
- **体积过滤**: 去除过小的噪声区域（默认<100像素）
- **格式转换**: 输出ImageJ兼容的.raw格式
- **统计分析**: 生成球体数量、体积等统计信息

### 技术特点
- **混合方法**: 结合深度学习的快速检测和传统方法的精确分割
- **参数可调**: 支持根据不同材质和扫描条件调节分割参数
- **内存优化**: 串行处理避免大数据量的内存溢出
- **多格式支持**: 兼容多种CT数据格式和输出格式

## 系统架构

```
WeltDetect/
├── main.py                 # 主程序入口（简洁版）
├── main_old.py            # 原始版本（包含自动安装依赖等功能）
├── main_complex.py        # 复杂版本（包含详细调试和可视化）
├── read_rec2.py           # rec2格式数据读取工具
├── src/                    # 核心模块
│   ├── data_loader.py      # 数据加载模块
│   ├── auto_focus.py       # 自动对焦模块
│   ├── ai_inference.py     # AI推理模块
│   ├── sphere_segmentation.py  # 3D球体分割模块
│   └── utils.py           # 工具函数
├── config/
│   └── config.yaml        # 配置文件
├── models/
│   └── best.pt           # YOLO模型文件
└── data/
    ├── raw/              # 原始数据
    ├── processed/        # 处理后数据
    └── output/           # 输出结果
```

## 文件版本说明

### main.py版本对比
- **main.py**: 当前使用的简洁版本，去除冗余输出，适合生产环境
- **main_old.py**: 最初版本，包含自动安装依赖、详细注释等功能
- **main_complex.py**: 功能完整版本，包含详细的调试信息、可视化、统计分析等

### 数据处理工具
- **read_rec2.py**: 专门用于处理rec2格式的CT数据文件，支持PNG提取和格式转换

## 模块功能

### 1. 数据加载模块 (data_loader.py)
- 支持bin格式数据（配合ImageParam.ini）
- 支持PNG序列数据
- 自动检测数据格式并加载

### 2. 自动对焦模块 (auto_focus.py)
- 基于梯度方差的对焦评估
- 自动寻找最佳对焦层
- 支持多峰值检测

### 3. AI推理模块 (ai_inference.py)
- YOLO目标检测
- 支持GPU/CPU推理
- 检测框后处理

### 4. 3D球体分割模块 (sphere_segmentation.py)
- 基于区域生长的3D分割
- 可调节分割参数
- 输出ImageJ兼容格式

## 快速开始

### 1. 环境配置
```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置文件
编辑 `config/config.yaml`:
```yaml
paths:
  model_path: "./models/best.pt"
  raw_data: "./data/raw"
  processed_data: "./data/processed"
  output: "./data/output"

device:
  name: "cuda"  # 或 "cpu", "mps"
```

### 3. 运行程序
```bash
# 调试模式（详细输出）
python main.py --debug --input ./data/processed/Slice_t

# 生产模式（快速运行）
python main.py --production --input ./data/processed/Slice_t

# 推荐参数（针对Slice_t数据，作为基础效果参照）
python main.py --production \
  --intensity-low 0.85 \
  --intensity-high 1.2 \
  --max-radius 18 \
  --min-size 100 \
  --input ./data/processed/Slice_t

# 自定义参数示例
python main.py --production \
  --intensity-low 0.85 \
  --intensity-high 1.2 \
  --max-radius 18 \
  --min-size 100
```

## 参数说明

### 分割参数
- `--intensity-low`: 强度下限容忍度 (0.8-0.95, 默认0.85)
  - 控制区域生长的下限阈值
  - 值越小包含更多低密度区域，可能引入噪声
  - 值越大只保留高密度区域，可能丢失边缘
  - **推荐值**: 0.85 (适用于Slice_t数据)

- `--intensity-high`: 强度上限容忍度 (1.05-1.3, 默认1.15)
  - 控制区域生长的上限阈值
  - 值越小分割更保守
  - 值越大可能引入伪影
  - **推荐值**: 1.2 (适用于Slice_t数据)

- `--max-radius`: 最大生长半径 (6-25, 默认8)
  - 限制区域生长的空间范围
  - 值越小避免扩散到远处伪影
  - 值越大允许更完整球体
  - **推荐值**: 18 (适用于Slice_t数据的大球体)

- `--min-size`: 最小球体体积 (50-200, 默认100)
  - 过滤小噪声区域
  - 值越小保留更多小球体
  - 值越大只保留大球体
  - **推荐值**: 100 (适用于Slice_t数据)

## 数据格式

### 输入数据
1. **bin格式**: 需要ImageParam.ini配置文件
2. **PNG序列**: 按层编号的PNG文件
3. **rec2格式**: 使用read_rec2.py工具预处理

### 输出数据
1. **3D分割结果**: `.raw`格式，可用ImageJ打开
2. **统计信息**: `.txt`格式，包含球体数量和体积信息

## rec2数据处理

如果你有新的rec2格式数据，使用read_rec2.py进行预处理：

```bash
# 提取rec2文件中的PNG图像
python read_rec2.py

# 然后使用提取的PNG数据运行主程序
python main.py --input ./extracted_png_folder
```

read_rec2.py功能：
- 从rec2文件中提取PNG图像序列
- 处理缺失PNG签名的情况
- 自动保存为标准PNG格式

## 二次开发

### 添加新的数据格式
在 `src/data_loader.py` 中添加新的加载方法：
```python
def load_new_format(self, data_path):
    # 实现新格式的加载逻辑
    pass
```

### 修改分割算法
在 `src/sphere_segmentation.py` 中修改 `simple_region_growing` 方法：
```python
def simple_region_growing(self, volume_data, seed_point, detection_box):
    # 实现新的分割算法
    pass
```

### 添加新的后处理
在 `src/sphere_segmentation.py` 中添加后处理方法：
```python
def post_process(self, mask):
    # 实现后处理逻辑
    pass
```

## 常见问题

### 1. 模型文件不存在
确保 `models/best.pt` 文件存在，或在配置文件中修改路径。

### 2. 内存不足
- 减少 `max_radius` 参数
- 使用生产模式（`--production`）
- 分批处理数据

### 3. 分割效果不佳
- 调整 `intensity_low` 和 `intensity_high` 参数
- 修改 `min_size` 过滤小噪声
- 检查输入数据质量

### 4. 运行速度慢
- 使用GPU加速（设置device为"cuda"）
- 使用生产模式跳过调试输出
- 减少 `max_radius` 参数

## 性能优化

### 典型运行时间（257个球体）
- 数据加载: ~0.2秒
- 自动对焦: ~0.9秒
- AI推理: ~1.0秒
- 3D分割: ~17秒
- 数据保存: ~10秒
- **总计**: ~30秒

### 优化建议
1. 使用GPU加速AI推理
2. 生产模式跳过调试输出
3. 合理设置分割参数
4. 预处理数据格式

## 许可证

MIT License
