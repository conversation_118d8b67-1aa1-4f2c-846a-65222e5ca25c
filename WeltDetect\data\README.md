# Data 目录说明

## 目录结构
```
data/
├── raw/           # 原始数据文件
│   ├── *.rec      # REC volume数据文件
│   ├── *.rec2     # REC2 压缩数据文件
│   └── *.vgi      # VGI 参数文件
├── processed/     # 处理后的数据
│   └── *.png      # 解压后的PNG切片
└── output/        # 输出结果
    └── results/   # 分割结果
```

## 使用说明

1. **原始数据**: 请将您的 `.rec`, `.rec2`, `.vgi` 文件放入 `raw/` 目录
2. **处理数据**: 运行预处理脚本后，PNG切片会保存在 `processed/` 目录
3. **输出结果**: 分割和检测结果会保存在 `output/` 目录

## 数据格式支持

- **REC文件**: 原始volume数据，需要配合VGI文件使用
- **REC2文件**: 压缩的volume数据，包含PNG切片
- **VGI文件**: 包含volume参数信息的配置文件

## 注意事项

- 请确保REC文件有对应的VGI文件
- REC2文件会被自动解压为PNG切片
- 大文件处理可能需要较长时间，请耐心等待
